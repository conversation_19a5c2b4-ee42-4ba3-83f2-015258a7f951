# Auto-Scroll During Chat Streaming - Implementation Report

## Feature Requirement
While streaming a message and the scroll position is at the bottom, keep it at the bottom when new content is added.

## Current Architecture Analysis

### Chat Streaming Flow
1. **Server Side**: `MessageServiceImpl.processNewMessageStreaming()` emits streaming events:
   - `UserMessageSaved` - User message is saved
   - `AssistantMessageStarted` - Assistant message begins with temporary ID
   - `AssistantMessageDelta` - Content chunks are streamed
   - `AssistantMessageCompleted` - Final message with permanent ID
   - `StreamCompleted` - Stream ends

2. **Client Side**: `DefaultSessionRepository.applyStreamEvent()` updates cached session data:
   - Updates message content incrementally during `AssistantMessageDelta` events
   - Triggers reactive state updates through `displayedMessages` StateFlow

3. **UI Layer**: `MessageList` component displays messages in a `LazyColumn` with `LazyListState`

### Current UI Components

#### MessageList Component
- **Location**: `app/src/commonMain/kotlin/eu/torvian/chatbot/app/compose/chatarea/MessageList.kt`
- **Key Elements**:
  - Uses `rememberLazyListState()` to manage scroll position
  - Wrapped in `ScrollbarWrapper` for platform-specific scrollbars
  - Displays `displayedMessages` from ChatViewModel
  - Uses `items()` with message IDs as keys for efficient recomposition

#### State Management
- **ChatViewModel**: Exposes `displayedMessages: StateFlow<List<ChatMessage>>`
- **ChatStateImpl**: Derives `displayedMessages` from session data reactively
- **Repository**: Updates message content during streaming via `applyStreamEvent()`

## Implementation Strategy

### 1. Scroll Position Detection
**Location**: `MessageList.kt`

Need to add scroll position monitoring to detect when user is at the bottom:
```kotlin
val isAtBottom by remember {
    derivedStateOf {
        val layoutInfo = lazyListState.layoutInfo
        val lastVisibleItem = layoutInfo.visibleItemsInfo.lastOrNull()
        lastVisibleItem?.index == layoutInfo.totalItemsCount - 1 &&
        lastVisibleItem.offset + lastVisibleItem.size <= layoutInfo.viewportEndOffset
    }
}
```

### 2. Auto-Scroll During Streaming
**Location**: `MessageList.kt`

Add effect to scroll to bottom when:
- New messages are added AND user was at bottom
- Message content is updated (streaming) AND user was at bottom

```kotlin
LaunchedEffect(displayedMessages.size, displayedMessages.lastOrNull()?.content) {
    if (isAtBottom) {
        lazyListState.animateScrollToItem(displayedMessages.size - 1)
    }
}
```

### 3. Enhanced Scroll Behavior
**Considerations**:
- **Smooth scrolling**: Use `animateScrollToItem()` instead of `scrollToItem()`
- **Performance**: Avoid excessive scroll operations during rapid delta updates
- **User control**: Stop auto-scrolling if user manually scrolls up during streaming

## Required Changes

### 1. MessageList.kt
- Add scroll position detection logic
- Add auto-scroll effect based on message changes
- Add streaming-aware scroll behavior

### 2. ChatAreaState.kt
- Add `isStreaming: Boolean` property
- Update state mapping in ChatViewModel

### 3. ChatStateImpl.kt / ChatState.kt
- Add streaming state tracking
- Expose streaming state through StateFlow

### 4. SendMessageUseCase.kt
- Update streaming state during message processing
- Set `isStreaming = true` when streaming starts
- Set `isStreaming = false` when streaming completes

## Technical Considerations

### Performance
- **Delta Updates**: During `AssistantMessageDelta` events, content changes frequently
- **Scroll Throttling**: May need to throttle scroll operations to avoid performance issues
- **Recomposition**: Ensure scroll detection doesn't cause unnecessary recompositions

### User Experience
- **Manual Override**: If user scrolls up during streaming, respect their choice
- **Visual Feedback**: Consider showing indicator when auto-scrolling is active
- **Accessibility**: Ensure screen readers handle dynamic content appropriately

### Platform Differences
- **Desktop**: Uses `VerticalScrollbar` - ensure scrollbar position updates correctly
- **Android/Web**: Native scrollbars should work automatically

## Implementation Priority

1. **High Priority**: Basic auto-scroll when at bottom during streaming
2. **Medium Priority**: Smooth animations and performance optimization
3. **Low Priority**: Advanced UX features like scroll indicators

## Testing Strategy

1. **Unit Tests**: Test scroll position detection logic
2. **Integration Tests**: Test streaming + scroll behavior
3. **Manual Testing**: Verify behavior across different platforms
4. **Edge Cases**: Test rapid streaming, long messages, user interruption

## Files to Modify

1. `app/src/commonMain/kotlin/eu/torvian/chatbot/app/compose/chatarea/MessageList.kt`
2. `app/src/commonMain/kotlin/eu/torvian/chatbot/app/compose/chatarea/ChatAreaState.kt`
3. `app/src/commonMain/kotlin/eu/torvian/chatbot/app/viewmodel/chat/state/ChatState.kt`
4. `app/src/commonMain/kotlin/eu/torvian/chatbot/app/viewmodel/chat/state/ChatStateImpl.kt`
5. `app/src/commonMain/kotlin/eu/torvian/chatbot/app/viewmodel/chat/usecase/SendMessageUseCase.kt`

## Conclusion

The implementation requires adding scroll position detection and auto-scroll logic to the `MessageList` component, along with streaming state tracking in the view model layer. The reactive architecture already provides the necessary data flow - we just need to add scroll behavior that responds to message updates while respecting user interaction.
