# Auto-Scroll During Chat Streaming - Development Plan

## Executive Summary

This development plan outlines the implementation of auto-scroll functionality for the chatbot application's message list during streaming operations. The feature will automatically keep the chat view scrolled to the bottom when new content is being streamed, but only when the user is already positioned at the bottom of the conversation.

## Feature Overview

### Problem Statement
Currently, when the AI assistant streams responses, users must manually scroll down to see new content as it appears. This creates a poor user experience, especially for longer responses where users lose track of the streaming content.

### Proposed Solution
Implement intelligent auto-scroll behavior that:
- Detects when the user is at the bottom of the message list
- Automatically scrolls to show new content during streaming
- Respects user intent by not auto-scrolling if they've manually scrolled up
- Provides smooth, performant scrolling animations
- Works consistently across all supported platforms (Desktop, Android, Web)

## Technical Requirements

### Dependencies
- **Kotlin 2.2.0** - Core language
- **Compose 1.8.2** with Material 3 - UI framework
- **kotlinx.datetime** - Time handling
- **Arrow 2.1.2** - Functional programming constructs
- **Existing Architecture**: Reactive state management with StateFlow, Repository pattern, Streaming events

### Architecture Integration
The solution integrates with the existing reactive architecture:
- **Server Side**: `MessageServiceImpl.processNewMessageStreaming()` already emits streaming events
- **Repository Layer**: `DefaultSessionRepository.applyStreamEvent()` updates cached session data
- **State Management**: `ChatStateImpl` derives `displayedMessages` from repository flows
- **UI Layer**: `MessageList` component displays messages in `LazyColumn` with `LazyListState`

## Implementation Approach

### Phase 1: Core Implementation (High Priority)

#### 1.1 Scroll Position Detection
**File**: `app/src/commonMain/kotlin/eu/torvian/chatbot/app/compose/chatarea/MessageList.kt`

Add scroll position monitoring logic:
```kotlin
val isAtBottom by remember {
    derivedStateOf {
        val layoutInfo = lazyListState.layoutInfo
        val lastVisibleItem = layoutInfo.visibleItemsInfo.lastOrNull()
        lastVisibleItem?.index == layoutInfo.totalItemsCount - 1 &&
        lastVisibleItem.offset + lastVisibleItem.size <= layoutInfo.viewportEndOffset
    }
}
```

#### 1.2 Streaming State Management
**Files**: 
- `app/src/commonMain/kotlin/eu/torvian/chatbot/app/viewmodel/chat/state/ChatState.kt`
- `app/src/commonMain/kotlin/eu/torvian/chatbot/app/viewmodel/chat/state/ChatStateImpl.kt`
- `app/src/commonMain/kotlin/eu/torvian/chatbot/app/compose/chatarea/ChatAreaState.kt`

Add `isStreaming: Boolean` property to track streaming state:
- Interface definition in `ChatState`
- Implementation in `ChatStateImpl` with private `MutableStateFlow`
- Integration in `ChatAreaState` data class

#### 1.3 Auto-Scroll Effect
**File**: `app/src/commonMain/kotlin/eu/torvian/chatbot/app/compose/chatarea/MessageList.kt`

Implement auto-scroll logic:
```kotlin
LaunchedEffect(displayedMessages.size, displayedMessages.lastOrNull()?.content) {
    if (isAtBottom) {
        lazyListState.animateScrollToItem(displayedMessages.size - 1)
    }
}
```

#### 1.4 Streaming State Updates
**File**: `app/src/commonMain/kotlin/eu/torvian/chatbot/app/viewmodel/chat/usecase/SendMessageUseCase.kt`

Update streaming state during message lifecycle:
- Set `isStreaming = true` when streaming starts
- Set `isStreaming = false` when streaming completes or errors

### Phase 2: Performance & UX Optimization (Medium Priority)

#### 2.1 Scroll Throttling
Implement throttling mechanism to prevent excessive scroll operations during rapid `AssistantMessageDelta` events:
- Use `debounce` or custom throttling logic
- Balance responsiveness with performance

#### 2.2 Smooth Animations
Replace basic scroll operations with smooth animations:
- Use `animateScrollToItem()` instead of `scrollToItem()`
- Configure appropriate animation duration and easing

#### 2.3 User Override Detection
Implement logic to detect manual user scrolling:
- Track user scroll gestures during streaming
- Disable auto-scroll when user scrolls up
- Re-enable when user returns to bottom

#### 2.4 Platform Compatibility
Ensure consistent behavior across platforms:
- Verify `ScrollbarWrapper` integration on Desktop
- Test native scrollbar behavior on Android/Web
- Handle platform-specific scroll characteristics

### Phase 3: Testing & Validation (Medium Priority)

#### 3.1 Unit Tests
Create comprehensive unit tests:
- Scroll position detection logic
- Streaming state management
- Auto-scroll trigger conditions

#### 3.2 Integration Tests
Test complete streaming + scroll flow:
- Mock streaming events using existing test utilities
- Verify state updates and UI reactions
- Use `MockK` for dependency mocking

#### 3.3 Manual Testing
Cross-platform validation:
- Desktop application testing
- Android device testing (if applicable)
- Web browser testing (if applicable)

#### 3.4 Edge Case Testing
Validate edge scenarios:
- Rapid streaming with high-frequency deltas
- Very long messages exceeding viewport
- Network interruptions during streaming
- User interaction during streaming
- Accessibility with screen readers

## Technical Challenges & Mitigation Strategies

### Challenge 1: Performance During Rapid Updates
**Risk**: Frequent `AssistantMessageDelta` events could cause performance issues
**Mitigation**: 
- Implement scroll throttling/debouncing
- Use `derivedStateOf` to minimize recompositions
- Profile performance with realistic streaming scenarios

### Challenge 2: User Experience Conflicts
**Risk**: Auto-scroll might interfere with user's reading or interaction
**Mitigation**:
- Only auto-scroll when user is already at bottom
- Implement user override detection
- Provide visual feedback when auto-scrolling is active

### Challenge 3: Platform Differences
**Risk**: Scroll behavior might vary across Desktop/Android/Web
**Mitigation**:
- Leverage existing `ScrollbarWrapper` abstraction
- Test thoroughly on all target platforms
- Use platform-agnostic Compose APIs

### Challenge 4: State Synchronization
**Risk**: Streaming state might get out of sync with actual streaming
**Mitigation**:
- Use reactive state management patterns
- Ensure proper cleanup in error scenarios
- Add comprehensive error handling

## Testing Strategy

### Unit Testing
- **Framework**: Kotlin Test + Compose Testing
- **Scope**: Scroll detection logic, state management
- **Location**: `app/src/commonTest/kotlin/`

### Integration Testing  
- **Framework**: MockK + Coroutine Test
- **Scope**: Complete streaming + scroll flow
- **Location**: `app/src/desktopTest/kotlin/`

### Manual Testing
- **Platforms**: Desktop (primary), Android, Web
- **Scenarios**: Normal streaming, edge cases, user interactions
- **Tools**: Gradle test tasks, platform-specific builds

## Timeline Considerations

### Development Phases
1. **Phase 1 (Core)**: 2-3 days
   - Basic scroll detection and auto-scroll
   - Streaming state management
   - Initial integration

2. **Phase 2 (Optimization)**: 2-3 days  
   - Performance improvements
   - User experience enhancements
   - Platform compatibility

3. **Phase 3 (Testing)**: 1-2 days
   - Comprehensive test coverage
   - Cross-platform validation
   - Edge case verification

### Total Estimated Effort: 5-8 days

## Success Criteria

### Functional Requirements
- ✅ Auto-scroll works when user is at bottom during streaming
- ✅ No auto-scroll when user has scrolled up
- ✅ Smooth, performant scroll animations
- ✅ Consistent behavior across platforms

### Non-Functional Requirements  
- ✅ No noticeable performance degradation during streaming
- ✅ Responsive to user interactions
- ✅ Accessible to screen readers
- ✅ Comprehensive test coverage (>80%)

## Files to Modify

### Core Implementation
1. `app/src/commonMain/kotlin/eu/torvian/chatbot/app/compose/chatarea/MessageList.kt`
2. `app/src/commonMain/kotlin/eu/torvian/chatbot/app/compose/chatarea/ChatAreaState.kt`
3. `app/src/commonMain/kotlin/eu/torvian/chatbot/app/viewmodel/chat/state/ChatState.kt`
4. `app/src/commonMain/kotlin/eu/torvian/chatbot/app/viewmodel/chat/state/ChatStateImpl.kt`
5. `app/src/commonMain/kotlin/eu/torvian/chatbot/app/viewmodel/chat/usecase/SendMessageUseCase.kt`

### Testing
6. `app/src/commonTest/kotlin/eu/torvian/chatbot/app/compose/chatarea/MessageListTest.kt` (new)
7. `app/src/desktopTest/kotlin/eu/torvian/chatbot/app/viewmodel/chat/ChatViewModelStreamingTest.kt` (new)

## Next Steps

1. **Review and Approval**: Validate plan with stakeholders
2. **Environment Setup**: Ensure development environment is ready
3. **Phase 1 Implementation**: Begin with core scroll detection logic
4. **Iterative Development**: Implement, test, and refine each phase
5. **Cross-Platform Testing**: Validate on all target platforms
6. **Documentation**: Update user documentation and code comments

## Conclusion

This development plan provides a structured approach to implementing auto-scroll functionality during chat streaming. The phased approach ensures core functionality is delivered first, followed by optimizations and comprehensive testing. The solution leverages the existing reactive architecture and maintains consistency with the project's technical standards and user experience goals.
